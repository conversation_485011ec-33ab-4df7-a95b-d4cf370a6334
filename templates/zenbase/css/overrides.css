/**
 * Custom CSS Overrides
 * This file contains custom overrides for the site's default styles.
 * It is loaded after all other CSS files to ensure it takes precedence.
 */

/* ==========================================================================
   Global Overrides
   ========================================================================== */

  body {
    overflow-x: hidden;
  }

  @font-face {
    font-family: 'fustatregular';
    src: url('/templates/zenbase/fonts/Fustat-VariableFont_wght.woff2') format('woff2-variations');
    font-weight: 200 800;
    font-style: normal;
  }

  body, h1, h2, h3, h4, h5, h6, .h1, .h2, .h3, .h4, .h5, .h6, p, a, b, strong {
    font-family: 'fustatregular', 'fustatregular', sans-serif !important;
  }

  a { color: #FE7720; }

  .btn.btn-primary {
    background-color: #FE7720;
    border-radius: 500px;
    color: #000;
    border: 1px solid #FE7720;
    min-height: auto;
    display: inline-flex;
    /* width: fit-content; */
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
  }

  .btn.btn-primary:hover {
    background-color: #FFB347;
    border: 1px solid #FFB347;
  }

  .btn.btn-primary img { height: 24px; }

   /* ==========================================================================
      Header Overrides
      ========================================================================== */

   .homepage .zen-header {
        background: transparent;
        position: absolute;
        top: 32px;
        left: 0;
        right: 0;
    }
   .zen-header__main { box-shadow: none; }
   .zen-header__main.fixed-menu {
       position: static !important;
       transform: none !important;
       box-shadow: none !important;
       background: transparent !important;
   }

   /* ==========================================================================
      Navigation Overrides
      ========================================================================== */


      .zen-menu__main .nav-header, .zen-menu__main .nav-link {
        text-transform: none;
        position: relative;
    }

    /* Fix for About Us menu links */
    li[data-item-id="362"] .zen-link--menu-sub,
    li[data-parent-id="362"] .zen-link--menu-sub,
    .menu-panel[data-parent-id="362"] .zen-link--menu-sub {
        cursor: pointer !important;
    }

    /* Ensure About Us menu links have proper URLs */
    .menu-panel[data-parent-id="362"] a.zen-link--menu-sub[href=""] {
        cursor: pointer !important;
        pointer-events: auto !important;
    }
    .sppb-addon-holiday-tabs .sppb-nav-tabs > div {
        margin-bottom: 0;
        background: transparent;
        padding: 16px 16px 7px 16px;
    }
    .sppb-addon-holiday-tabs .sppb-nav-tabs .sppb-tab {
        width: 162px;
        white-space: normal;
        font-size: 14px;
        font-weight: 500;
        line-height: normal;
        border-bottom-left-radius: 0;
        border-bottom-right-radius: 0;
        border-width: 0 0 8px 0;
        border-color: transparent;
        border-style: solid;
        transition: none;
        text-decoration: none;
    }

    .sppb-addon-holiday-tabs .sppb-nav-tabs .sppb-tab a {
        text-decoration: none;
        color: inherit;
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 9px;
    }

    .sppb-addon-holiday-tabs .sppb-nav-tabs .sppb-tab.active {
        border-color: #fe7720;
    }
    .sppb-addon-holiday-tabs .sppb-nav-tabs > div,
    .sppb-addon-holiday-tabs .sppb-nav-tabs > a {
        gap: 9px;
    }
    .sppb-addon-holiday-tabs .sppb-nav-tabs .sppb-tab svg {
        width: 40px;
        height: 40px;
    }
    .sppb-tab-icon {
        margin-right: 0!important;
    }
    .zen-body__main { padding: 0; }

    @media screen and (max-width: 576px) {
        .sppb-addon-holiday-tabs .sppb-nav-tabs .sppb-tab:not(.active) {
            border: 1px solid #fff;
            margin-left: -1px;
        }
        .zen-body__main {
            padding: 30px 0 0;
        }
        #trip-extensions-tab-content .container a,
        #trip-extensions .container a {
            align-self: flex-start;
            margin: 10px 0;
        }

        #trip-extensions-tab-content .container > div,
        #trip-extensions .container > div {
            flex-direction: column;
        }

    }



    /* .zen-header__info { opacity: 0;} */
   /* ==========================================================================
      Content Overrides
      ========================================================================== */



   /* ==========================================================================
      Holiday Cards & Grids
      ========================================================================== */

   .sppb-addon-holiday-tabs { margin-top:-70px; }

   /* Base styles for tab panes */
   .sppb-addon-holiday-tabs .sppb-tab-content > .sppb-tab-pane {
       padding-left: 15px;
       padding-right: 15px;
   }

   .sppb-tabs .category-header {
    max-width: 1400px;
    margin-left: auto;
    margin-right: auto;
}

   @media (max-width: 576px) {
       /* Remove padding/margin from scrollable container */
       .sppb-addon-holiday-tabs .sppb-tab-content {
           padding-left: 0;
           padding-right: 0;
           margin-left: 0;
           margin-right: 0;
       }

       /* Reset and set proper padding for tab panes */
       .sppb-addon-holiday-tabs .sppb-tab-content > .sppb-tab-pane {
           padding-left: 0 !important;
           padding-right: 0 !important;
       }

       /* Ensure category header has proper margins */
       .sppb-addon-holiday-tabs .sppb-tab-pane .category-header {
           padding-left: 15px;
           padding-right: 15px;
           margin-left: 0;
           margin-right: 0;
           margin-top: 20px;
           margin-bottom: 0;
       }

       /* Scrollable container */
       .sppb-addon-holiday-tabs .holiday-grid-container {
           overflow-x: auto;
           scroll-behavior: smooth;
           -webkit-overflow-scrolling: touch;
           margin: 0;
           padding: 0;
           scrollbar-width: none; /* Firefox */
           -ms-overflow-style: none; /* IE and Edge */
       }

       .sppb-addon-holiday-tabs .holiday-grid-container::-webkit-scrollbar {
           display: none; /* Chrome, Safari, Opera */
       }

       /* Flexbox layout for horizontal scrolling */
       .sppb-addon-holiday-tabs .holiday-grid {
           display: flex;
           flex-wrap: nowrap;
           gap: 30px;
           margin-left: 15px;
           margin-right: 15px;
           padding: 0;
           overflow-x: visible;
           width: fit-content;
       }

       /* Set card width to show ~1.25 cards at 375px viewport (300px cards) */
       .sppb-addon-holiday-tabs .holiday-grid .zen-card {
           flex: 0 0 300px; /* Fixed width, no grow/shrink */
           width: 300px;
       }

       /* Removed scroll-snap-align from zen-card */
       .sppb-addon-holiday-tabs .holiday-grid .zen-card {
       }

       .sppb-addon-holiday-tabs .sppb-nav-tabs > div {
           border: 1px solid #fff;
       }
   }

   .sppb-addon-holiday-tabs .sppb-tab-content {
        /* min-height: 800px!important; */

   }

   .loading-spinner {
    margin-top: 100px;
   }


   /* ==========================================================================
      Footer Overrides
      ========================================================================== */



   /* ==========================================================================
      Responsive Overrides
      ========================================================================== */

   @media (max-width: 1199px) {
       /* Large devices */
    .zen-utility-bar {
        Position: fixed;
        top: 0;
        width: 100%;
        z-index:99999;
        transform: translateZ(0);
       }
       body { margin-top: 32px;}
   }

   @media (max-width: 992px) {
       /* Medium devices */
       html, body {
        margin-right: -1px;
       }
       .modal-backdrop {
        width: 100%;
       }

   }

   @media (max-width: 768px) {
       /* Small devices */
   }

   @media (max-width: 576px) {
       /* Extra small devices */
       .zen-header {
           overflow: hidden;
           position: static;
       }
   }
   .zen-header {
    max-width: 1920px;
    margin: 0 auto;
margin-bottom: 28px;
}

.sppb-addon-holiday-tabs .sppb-nav-tabs {
    display: flex;
    overflow-x: auto;
    overflow-y: visible;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
    -webkit-overflow-scrolling: touch;
    margin-bottom: 20px;
    list-style: none;
    padding: 0 15px;
    margin: 0;
    border-bottom: none;
    position: relative;
    z-index: 2;
    max-width: 1400px;
    margin-left: auto;
    margin-right: auto;
}

.sppb-nav-tabs-container {
    background: linear-gradient(to bottom, rgba(0,0,0,0) 0%, rgba(0,0,0,1) 100%);
}

.zen-card__body, .card-body {
    padding: 0;
    display: flex;
    flex-direction: column;
    height: 100%;
}
.zen-card:not(.zen-content-search .zen-card) {
    border-radius: 10px;
    overflow: hidden;
    display: flex;
    /* align-items: stretch; */
}

.zen-card__image img {
    width: 100%;
    border: none;
}

.zen-text--subtitle {
    text-transform: none;
}

.btn, .btn-primary {
    text-transform: none!important;
}

.zen-utility-bar {
    align-items: center;
    background-color: black;
    color: white;
    display: flex;
    height: 32px;
    justify-content: space-between;
    padding: 10px 12px;
}

.top-social a {
    color: white;
}
.top-ratings {
    display: flex;
    align-items: center;
    gap: 10px;
}
.top-login {
    font-size: 14px;
    line-height: 20px;
    font-weight: 700;
}

.zen-text, .zen-card ul, .zen-price * {
    font-size: 14px!important;
}

.zen-card ul {
    padding-left: 1rem;
    margin-bottom: 0;
}

.zen-card__info + .zen-text {
    margin-bottom: 0 !important;
}

.zen-title {
    font-size: 18px;
    font-style: normal;
    font-weight: 700;
    line-height: 22px;
}

.zen-price {
    padding-top: 8px;
    padding-bottom: 8px;
    background-color: #f2f2f2;
    align-self: flex-end;
    width: 100%;
    margin-top: auto;
}

.zen-text--subtitle {
    display: flex;
    justify-content: space-between;
}

.zen-title:not(.zen-content-search .zen-title),
.zen-card__info-wrap .zen-text,
.zen-card__info-wrap > div {
    padding-left: 12px !important;
    padding-right: 12px !important;
}

/* Default styling for holidaytabs (no Bootstrap grid) */
.zen-card__info {
    padding-left: 12px !important;
    padding-right: 12px !important;
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

/* Override for holiday search cards that use Bootstrap grid */
.zen-card__info:has(.row) {
    display: block;
    justify-content: unset;
    align-items: unset;
    padding-left: 0 !important;
    padding-right: 0 !important;
}

/* Default styling for holidaytabs columns (direct flex children) */
.zen-card__info > div:not(.row) {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 8px;
}

/* Bootstrap grid columns in holiday search */
.zen-card__info .row .col {
    padding-left: 0;
    padding-right: 0;
}

/* Style the flex containers inside Bootstrap columns */
.zen-card__info .row .col .d-flex {
    width: 100%;
}

.zen-card__info span {
    line-height: 14px;
    margin: 0 0 0 2px;
}

.zen-card .col {
    padding-left: 12px !important;
    padding-right: 12px !important;
}

.zen-card__info-wrap :not(.zen-title) {
    font-weight: 600;
}

.zen-card__info-wrap .zen-text--text-df * {
    font-weight: 400!important;
}
.zen-card a {
    position: relative;
    z-index: 1;
}
.zen-card__info-wrap {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    align-items: stretch;
}

.zen-flex-end {
    /* margin-top: 0; */
}

.eapps-facebook-feed-container {
    margin: 0 auto 0 0!important;
}

.bg-black { background-color: black!important; }
.bg-orange { background-color: #fe7720!important; }
.bg-white { background-color: white!important; }

.fg-white { color: white!important; }
.fg-orange { color: #fe7720!important; }

.bd-white { border-color: white!important; }

.btn { min-height: 38px;}
.btn-outline, .btn-outline-2 { border-width: 1px!important; background-color: transparent!important;border-style: solid!important; border-color: black!important;  }
.btn-outline-1 { border-width: 1px!important; background-color: transparent!important;border-style: solid!important; border-color: black!important;  }
.btn-outline.bd-black, .btn-outline-1.bd-black, .btn-outline-2.bd-black { border-color: black!important; }
.btn-outline.bd-white, .btn-outline-1.bd-white, .btn-outline-2.bd-white {border-color: white!important; }
.btn-outline:hover, .btn-outline-1:hover, .btn-outline-2:hover { color: black!important; }
.btn-primary.text-white { color: white!important; }

.border-black { border-color: black!important; }

.comm-col-1 .sppb-column-addons, .comm-col-2 .sppb-column-addons {
    display: flex;
    justify-content: space-between;
    flex-direction: column;
}


.sppb-container-inner {
    margin-left: auto;
    margin-right: auto;
}

.zen-card__body button {
    border: none;
    background-color: transparent;
    margin-right: 0;
    font-size: inherit;
    font-weight: inherit;
    line-height: inherit;
}

@media (min-width: 576px) {
    .zen-footer .sppb-container-inner {
      max-width: 760px;
      width: 100%; } }
  @media (min-width: 768px) {
    .zen-footer .sppb-container-inner {
      max-width: 940px;
      width: 100%; } }
  @media (min-width: 992px) {
    .zen-footer .sppb-container-inner {
      max-width: 1100px;
      width: 100%; } }
  @media (min-width: 1200px) {
    .zen-footer .sppb-container-inner {
      max-width: 1440px;
      width: 100%; } }

/* Popover z-index fixes */
.popover {
    z-index: 9999 !important;
}
.popover-content {
    position: relative;
    z-index: 10000;
}
.popover button {
    position: relative;
    z-index: 10001;
}

.zen-card__info, .card-info, .zen-card__image-overlay {
    font-family: "fustatregular", sans-serif;
    font-size: 14px;
}

.zen-card__image-overlay .zen-list.zen-list--inline {
    display: flex;
    flex-wrap: nowrap;           /* Default: single line with cropping */
    justify-content: center;
    align-items: center;
    overflow: hidden;
    padding-left: 0;             /* Remove left padding from ul */
    transition: all 0.3s ease;
}

/* Allow wrapping when overlay expands on hover */
.zen-link--image-overlay-expand:hover .zen-card__image-overlay .zen-list.zen-list--inline {
    flex-wrap: wrap;             /* Allow wrapping on hover */
    overflow: visible;           /* Show all content when expanded */
    padding-left: 10px;          /* Add horizontal padding when expanded */
    padding-right: 10px;
}

/* Maintain consistent spacing and prevent compression */
.zen-card__image-overlay .zen-list.zen-list--inline li {
    white-space: nowrap;
    text-overflow: clip;
    flex-shrink: 0;              /* Prevent items from shrinking */
    flex-grow: 0;                /* Prevent items from growing */
    margin-right: 0.5rem;        /* Consistent 8px spacing between months */
}

/* Remove margin from the last item */
.zen-card__image-overlay .zen-list.zen-list--inline li:last-child {
    margin-right: 0;
}

.difficulty-info-section {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.difficulty-info-btn {
    background: none;
    border: none;
    padding: 0;
    margin: 0;
    cursor: pointer;
    display: inline-flex!important;
    align-items: center;
    justify-content: center;
    gap: 0;
}

.home-hero {
    min-height: 688px;
}


/* SEARCH */

.zen-search__content {
    background-color: #e4e8ed;
}

.zen-search__content .card {
    border-radius: 10px;
    overflow: hidden;
}
.zen-facet__body {
    background-color: white;
    border-radius: 10px;
    border: none;
}

.zen-form:not(#searchForm) {
    border: 1px solid black;
    border-radius: 9999px;
}

.zen-form input:where(:not(.zen-content-search__main-form input))
 {
    border: none;
    background: transparent;
    padding: 8px;
}

.zen-form input:focus {
    background: transparent;
}

.zen-form button:not(.zen-content-search__filters button), .zen-form span {
    background: transparent;
    border: none;
}

.zen-form .zen-btn {
    min-height: auto;
    padding: 7px 10px;
}

.zen-form .zen-btn:hover,
.zen-form .zen-btn:focus {
    background-color: transparent;
}

.zen-btn--icon-submit:before {
    display:none
}

.zen-list--facet li:before {
    border: 1px solid black;
}

.zen-list--facet li.active:before {
    border: none;
    background-color: #ed7f3b;
}

.zen-list--facet li.active:after {
    background-image: url(/templates/zenbase/icons/check.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-color: transparent;
}

.zen-facet__clear {
    bottom: auto;
    color: #aaa;
    font-size: 13px;
    text-decoration: underline;
}

.zen-card__image-overlay {
    font-family: 'fustatregular', sans-serif;
}

.zen-list--menu-dropdown {
    display: none;
    background: white !important;
    margin: 0;
    padding: 0;
}

/* Menu Container */
header .zen-menu-container {
    position: relative;
    /* background: white; */
    border-radius: 4px;
    /* box-shadow: 0 2px 4px rgba(0,0,0,0.1); */
    z-index: 1001;
}

/* Panels Container */
header .menu-panels-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    pointer-events: none;
}

header .menu-panels-container .menu-panel {
    pointer-events: auto;
}

/* Panels */
header .menu-panel {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    background: #fff;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-radius: 0 0 10px 10px;
    min-width: 500px;
    height: auto;
    min-height: 50px;
    overflow: visible;
    padding: 20px;
    width: max-content;
    display: none;
    pointer-events: none;
}

header .menu-panel__column > div *,
header .menu-panel__column > ul *:not(img) { width: 100%!important; }

/* Remove min-width for Why Evertrek menu */
header div[data-parent-id="362"].menu-panel,
header div[data-parent-id="362"].menu-panel:not(.hidden) {
    min-width: auto !important;
    width: auto !important;
}

/* Show panel when not hidden */
header .menu-panel:not(.hidden) {
    display: flex !important;
    pointer-events: auto;
}

/* Adventures menu styles */
header li[data-parent-id="605"] .menu-panel {
    min-width: 800px !important;
}

/* Remove min-width for Why Evertrek menu */
header li[data-parent-id="362"] .menu-panel {
    min-width: auto !important;
}

header div[data-parent-id="605"] .menu-panel__column:nth-child(2) {
    width: fit-content !important;
    flex: none !important;
    border-left: 1px solid rgba(0, 0, 0, 0.20) !important;
    padding-left: 15px !important;
    /* padding-right: 15px !important; */
}

header li[data-parent-id="605"] .zen-link--menu-sub.active {
    background-color: #f5f5f5 !important;
    color: #000 !important;
}

/* First Level */
header .zen-list--menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    /* background: white; */
    border-radius: 4px;
}

header .zen-list--menu > li {
    position: relative;
}

header .zen-list--menu .zen-link {
    display: block;
    padding: 15px 20px; /* padding: 15px 20px; */
    color: #333;
    text-decoration: none;
    white-space: nowrap;
}

/* Second Level */
header .zen-list--menu-dropdown {
    display: block !important;
    list-style: none;
    margin: 0;
    padding: 10px 0;
    min-width: 250px;
    background: white;
    height: 100%;
    position: static;
}

/* Third and Fourth Level */
header .third-level-container > .zen-list--menu-dropdown {
    display: block !important; /* Show third level menus by default */
}

header .fourth-level-container > .zen-list--menu-dropdown {
    display: none !important; /* Hide fourth level menus by default */
}

header .fourth-level-container > .zen-list--menu-dropdown.show,
header .fourth-level-container:hover > .zen-list--menu-dropdown {
    display: block !important; /* Show fourth level menus when they have the show class or are being hovered over */
}

header .zen-link--menu-sub {
    display: block;
    padding: 8px 20px;
    padding-right: 32px; /* Space for the arrow (24px) plus extra padding */
    color: #333;
    text-decoration: none;
    white-space: nowrap;
    position: relative; /* Add this to ensure proper arrow positioning */
}

/* Hover States */
header.zen-link:hover,
header.zen-link--menu-sub:hover {
    background-color: #f5f5f5;
}

/* Active States */
header .active > .zen-link,
header .active > .zen-link--menu-sub {
    font-weight: bold;
}

/* Jon additions */

header .zen-list--menu-sub, header .zen-menu__main .zen-list--menu-dropdown {
    height: auto;
    top: auto;
    width: fit-content;
}

header .menu-panel__columns {
    display: flex;
    flex-direction: row;
    gap: 0;
    width: 100%;
}

header .menu-panel__column {
    display: flex;
    flex-direction: column;
    min-width: 190px;
}

header .menu-panel__column:first-child {
    padding-right: 15px;
    min-width: 190px;
}

header .menu-panel__column:nth-child(2):not([data-parent-id="605"]):not([data-parent-id="614"] *) {
    padding-left: 15px;
    /* padding-right: 15px; */
    border-left: 1px solid rgba(0, 0, 0, 0.20);
    width: 220px;
    flex: 0 0 220px;
    display: flex !important;
}

header .menu-panel__column:nth-child(3) {
    padding-left: 15px;
    width: 420px;
    position: relative;
    display: flex !important;
}

header .menu-panel__image {
    width: calc(100% - 20px);
    height: 250px;
    border-radius: 10px;
    overflow: hidden;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
    background-color: #f5f5f5; /* Light grey fallback */
    margin-left: auto; /* This will push it to the right */
}

header .menu-panel__image-title {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 20px;
    background: linear-gradient(to top, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 100%);
    color: white;
    font-size: 32px;
    line-height: 38px;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Single column menu panels */
header .menu-panel__columns:only-child .menu-panel__column:only-child {
    padding-right: 0;
    border-left: none;
    padding-left: 0;
}

header .zen-list--menu-dropdown {
    width: 190px;
}

header .zen-link--menu-sub:hover,
header .zen-link--menu-sub:focus,
header .zen-link--menu-sub.active {
    background-color: #e3e8ee;
    color: black;
    border-radius: 9999px;
}

header .zen-link--menu-sub:hover::after, header .zen-link--menu-sub:focus::after {
    content: '';
    position: absolute;
    right: 7px;
    top: 50%;
    transform: translateY(-50%);
    width: 24px;
    height: 24px;
    background-image: url('/templates/zenbase/icons/keyboard_arrow_right.svg');
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
}

.nav-item.dropdown:nth-of-type(2) .menu-panel__column:first-of-type a { min-width: 226px;}

/* Keep continent highlighted when hovering over "All in" link */
header li[data-parent-id="605"] .menu-panel__column:nth-child(2) .zen-link--menu-sub.active,
header li[data-parent-id="605"] .menu-panel__column:nth-child(2) .zen-link--menu-sub.active:hover {
    background-color: #e3e8ee;
    color: black;
    border-radius: 9999px;
    position: relative;
    padding-right: 32px;
}

/* Add arrow to active menu items */
header .zen-link--menu-sub.active::after {
    content: '';
    position: absolute;
    right: 7px;
    top: 50%;
    transform: translateY(-50%);
    width: 24px;
    height: 24px;
    background-image: url('/templates/zenbase/icons/keyboard_arrow_right.svg');
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
}

header .zen-menu .dropdown-toggle:after {
    content: ""
}

.rounded { border-radius: 9999px!important; }

.mobile-bar {
    display:none!important;
}

@media screen and (max-width: 576px) {
    .zen-header__search {
        display:none!important;
    }
    .zen-card > .zen-link {
        width: 100%;
    }
}

.zen-card--message h3 {
    text-transform: none!important;
    font-size: 24px;
    line-height: 26px;
    padding: 20px 70px 20px 30px!important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.20);
}

.zen-card--message img {
    width: 42px;
    height: auto;
    margin-right: 20px!important;
}

.zen-card--message ul {
    padding-left: 30px;
    padding-right: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.20);
    margin-bottom: 20px;
}


.zen-pagination__link, .zen-blog__article-pagination li, .zen-blog__article-pagination a, .pagination .page-item .page-link {
    text-decoration: none;
    transition: none;
    color: #0095CC;
}

.zen-link--pagination, .zen-pagination__link, .zen-blog__article-pagination li {
    border: none;
    background: transparent;
    justify-content: center;
    align-items: center;
    border-radius: 5px;
    display: flex;
    height: 40px;
    color: #23383C;
    width: 40px;
    margin: 3px;
}

.zen-link--pagination-active, .zen-list--pagination span, .zen-pagination__inner span, .pagination .page-item.active .page-link {
    border-color: none;
    background: #fe7720;
    color: black;
    border-radius: 9999px;
}

.zen-blog__article-pagination a,  .zen-footer__menu .zen-pagination__link, .zen-footer__menu .zen-blog__article-pagination li, .zen-blog__article-pagination .zen-footer__menu li, .zen-footer__menu .zen-blog__article-pagination a, .zen-blog__article-pagination, .zen-footer__menu .pagination .page-item .page-link, .pagination .page-item .zen-footer__menu .page-link  {
    color: black;
}

.zen-pagination__item-alternate a,
.pagination__last .zen-pagination__link {
    background: #a5a6a7;
}

.zen-back-to-top {display: none!important;}

@media screen and (max-width: 576px) {
    /* .zen-body { margin-top: 25px; } */
    .zen-results-header {margin-top: 15px;}
}

/* Itinerary Map Styles */
.zen-itinerary-map {
  margin: 2rem 0;
  /* background: #fff; */
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.itinerary-map {
    border: 1px solid #c6c6c6; border-radius: 10px; overflow: hidden;
}

.zen-itinerary-accordion {
  height: auto;
  min-height: 400px;
  max-height: 600px;
  overflow-y: auto;
  padding: 1rem;
}

#itineraryAccordion .zen-btn--accordion-sub,
#dates-pricing-accordion .zen-btn--accordion-sub,
#faqAccordion .zen-accordion-item {
    font-size: 14px;
    font-weight: 600;
}

#itineraryAccordion .accordion-content:before,
#dates-pricing-accordion .accordion-content:before,
#faqAccordion .accordion-content:before {
    border-top: 1px solid #909090;
    height: 1px;
    width: 100%;
    content: '';
    display: block;
    margin: 10px 0 0 0;
}

#itineraryAccordion .accordion-container,
#dates-pricing-accordion .accordion-container,
#faqAccordion .accordion-container {
    border: 1px solid #909090;
    background: #fff;
    border-radius: 10px;
    padding: 10px 14px;
    margin-bottom: 10px;
}

#dates-pricing-accordion .container > .row:not(:last-child) {
    padding-bottom: 15px!important;
    border-bottom: 1px solid #909090;
    margin-bottom: 10px;
}

#itineraryAccordion .zen-accordion-meals {
    color:#909090;
}

#itineraryAccordion .zen-accordion-item {
  margin-bottom: 1rem;
  border: 1px solid rgba(0, 0, 0, 0.125);
  border-radius: 0.25rem;
}

.zen-accordion-item:last-child {
  margin-bottom: 0;
}

.zen-accordion-header {
  padding: 1rem;
  background: #f8f9fa;
  cursor: pointer;
  position: relative;
}

.zen-accordion-header:after {
  content: '+';
  position: absolute;
  right: 1.5rem;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.5rem;
  color: #666;
}

.zen-accordion-header h3 {
  margin: 0;
  font-size: 1.1rem;
  color: #333;
}

.zen-accordion-content {
  display: none;
  padding: 1rem;
  background: #fff;
  border-top: 1px solid #eee;
}

.zen-accordion-item.active .zen-accordion-content {
  display: block;
}

.zen-accordion-item.active {
  border-color: #FE7720;
}

.zen-accordion-item.active .zen-accordion-header {
  background: #FE7720;
}

.zen-accordion-item.active .zen-accordion-header h3 {
  color: white;
}

.zen-accordion-item.active .zen-accordion-header:after {
  content: '−';
  color: white;
}

.zen-accordion-intro {
  margin-bottom: 1rem;
  color: #666;
}

/* Map container */
#itinerary-map {
  height: 100%;
  min-height: 400px;
}

@media (max-width: 768px) {
  .zen-itinerary-map .row {
    flex-direction: column;
  }

  .zen-itinerary-accordion,
  #itinerary-map {
    min-height: 300px;
  }

  .zen-itinerary-accordion {
    margin-bottom: 1rem;
  }
}

/* Map Placeholder */
.zen-map-placeholder {
  height: 100%;
  min-height: 400px;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 2rem;
}

.zen-map-placeholder__content {
  color: #6c757d;
}

.zen-map-placeholder__content p {
  margin: 0;
  font-size: 1.1rem;
}

header .zen-list--menu-sub li, .zen-menu__main .zen-list--menu-dropdown li {
    border: none;
}

.zen-body__breadcrumbs {display: none!important;}

header .menu-panel__column:first-child {
    padding-right: 15px;
}

/* Single column menu panels */
header .menu-panel__columns:only-child .menu-panel__column:only-child {
    padding-right: 0;
    border-left: none;
    padding-left: 0;
}

header .zen-list--menu-dropdown {
    width: 190px;
}

/* Add active state for menu items */
header .zen-link--menu-sub.active {
    background-color: #e3e8ee;
    color: black;
    border-radius: 9999px;
}

/* Highlight active section in Adventures menu */
header li[data-parent-id="605"] .menu-panel__column:nth-child(2) .zen-link--menu-sub:hover ~ .third-level-container ul[data-parent-id],
header li[data-parent-id="605"] .menu-panel__column:nth-child(2) .zen-link--menu-sub:hover {
    background-color: #e3e8ee;
    color: black;
    border-radius: 9999px;
}

header li[data-parent-id="605"] .menu-panel__column:nth-child(2) .zen-link--menu-sub:hover {
    position: relative;
}

/* Keep continent highlighted when hovering over "All in" link */
header li[data-parent-id="605"] .menu-panel__column:nth-child(2) .zen-link--menu-sub.active,
header li[data-parent-id="605"] .menu-panel__column:nth-child(2) .zen-link--menu-sub.active:hover {
    background-color: #e3e8ee;
    color: black;
    border-radius: 9999px;
}

header li[data-parent-id="605"] .menu-panel__column:nth-child(2) .zen-link--menu-sub:hover ~ .first-column-item[data-item-id="${parentId}"] .zen-link--menu-sub {
    background-color: #e3e8ee;
    color: black;
    border-radius: 9999px;
}


.zen-header__logo img {
    width: 237px;
    max-width: 237px;
}

.zen-cta__featurebox-content { text-transform: none!important; }

/* Typography */

h1, .h1 { font-size: 36px; line-height: 42px; font-weight: 800; color: #000; }
h2, .h2 { font-size: 40px; line-height: 44px; font-weight: 800; }
h3, .h3 { font-size: 18px; line-height: 22px; font-weight: 700; }
h4, .h4 { font-size: 18px; line-height: 22px; font-weight: 700; }
h5, .h5, header .zen-list--menu .zen-link, .video-button { font-size: 16px; line-height: normal; font-weight: 700; }

h3.reviews-title { font-size: 22px; line-height: 26px; font-weight: 800; }

p, ul, ol, th, td { font-size: 14px; line-height: normal; font-weight: 400; }
th, td { padding: 7px; }
p strong { font-size: 14px; line-height: normal; font-weight: 600; }
.zen-btn--accordion-sub {
  font-family: fustatregular;
  font-size: 14px;
  line-height: normal;
  font-weight: 400;
  padding: 0;
  border-bottom: none;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}
.zen-btn--accordion-sub > span {
  display: flex;
}
.zen-btn--accordion-sub:hover {
  color: inherit;
  background: inherit;
  border-color: inherit;
}
.zen-btn--accordion-sub:focus {
  color: black;
  background: inherit;
  border-color: inherit;
}
.zen-btn--accordion-sub:after {
  content: "";
  background-image: url("/templates/zenbase/icons/chevron-down.svg");
  background-repeat: no-repeat;
  background-size: contain;
  width: 24px;
  height: 24px;
  display: inline-block;
  vertical-align: middle;
  margin-left: 8px;
  flex-shrink: 0;
}
.zen-btn--accordion-sub .day {
  color: #fe7720;
  font-weight: 700;
  font-size: 16px;
  min-width: 48px;
}
.zen-btn--accordion-sub .title {
  /* margin-left: 20px; */
    margin-right: 5px;
  font-weight: 600;
}
.zen-btn--accordion-sub .title-elevation {
  margin-left: 20px;
}
.zen-btn--accordion-sub .elevation {
  color: #909090;
  font-weight: 600;

}
.accordion-container > div { padding: 9px 0; }
/* #dates-pricing-accordion > div.accordion-container > div[id^="price-month-title-"] { padding: 9px 0; } */
p.intro { font-size: 18px; line-height: normal; font-weight: 600; }

.btn, .btn.btn-primary, a.sppb-btn, a.sppb-btn.sppb-btn-default, .sppb-readmore, .zen-btn {
    font-family: fustatregular;
    font-size: 14px; line-height: 21px; font-weight: 500; text-transform: none!important; border-radius: 9999px;  background-color: #FE7720; color: black; border-color: #FE7720; padding: 6px 12px; min-height: auto; }

.btn.disabled { color: white; background-color: #909090; border-color: #909090; }

.btn.text-white { color: white!important; }

.btn:hover, a.sppb-btn:hover, a.sppb-btn.sppb-btn-default:hover, .sppb-readmore:hover, .zen-btn:hover { background-color: #FFB347; border-color: #FFB347; }

.tab { font-size: 14px; line-height: 21px; font-weight: 500; text-transform: none!important; }

.sub-title { font-size: 20px; line-height: 26px; font-weight: 800; }
.sub-copy { font-size: 12px; line-height: normal; font-weight: 400; }
p.community-intro { font-size: 16px; line-height: 20px; font-weight: 400; }

.desktop-nav li { font-size: 14px; line-height: normal; font-weight: 500; }
.mobile-nav li { font-size: 16px; line-height: normal; font-weight: 700; }
.mobile-nav .back { font-size: 14px; line-height: normal; font-weight: 600; }

.zen-utility-bar .top-ratings { font-size: 12px; line-height: 26px; font-weight: 400; }
.zen-utility-bar .top-ratings strong { font-size: 12px; line-height: 26px; font-weight: 700; }
.zen-utility-bar .top-login, .zen-utility-bar .top-whatsapp { font-size: 14px; line-height: normal; font-weight: 700; }

.nav-wrapper {display: flex; align-items: center; justify-content: space-between;}
.zen-header__menu {display: none;}

.zen-results-sort button {
  z-index: 1;
  font-family: fustatregular;
  font-size: 14px;
  line-height: 150%;
  font-weight: 500;
}

.zen-results-sort .dropdown-toggle::after {
  content: '';
  display: inline-block;
  width: 24px;
  height: 25px;
  background-image: url("data:image/svg+xml,%3Csvg width='24' height='25' viewBox='0 0 24 25' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cg clip-path='url(%23clip0_1060_3236)'%3E%3Cpath d='M16.59 9.09009L12 13.6701L7.41 9.09009L6 10.5001L12 16.5001L18 10.5001L16.59 9.09009Z' fill='white'%3E%3C/path%3E%3C/g%3E%3Cdefs%3E%3CclipPath id='clip0_1060_3236'%3E%3Crect width='24' height='24' fill='white' transform='translate(0 0.5)'%3E%3C/rect%3E%3C/clipPath%3E%3C/defs%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  border: none;
  margin-left: 0;
  vertical-align: middle;
}

#refine { z-index: 1; font-family: fustatregular; font-weight: 500; }

/* Desktop specific */

/* @media Query */
@media screen and (min-width: 576px) {
    h1, .h1 { font-size: 48px; line-height: 50px; font-weight: 800; }
    h3, .h3, h3.reviews-title  { font-size: 24px; line-height: 26px; font-weight: 800; }
    .zen-utility-bar {
        padding: 10px 20px;
    }
    .zen-header__menu {display: flex; width: 100%;}
    .zen-menu {width: 100%;}
    .zen-menu__main .zen-list--menu  { justify-content: space-between; margin-left: 75px; }
    .zen-menu__main .zen-list--menu .nav-item:last-child .nav-link { padding-right: 0; }
    .zen-holiday__tabs .conditional-break {
        display: block;     /* Force line break on desktop */
        width: auto;
        height: 0;
        overflow: hidden;        /* No space on mobile by default */
      }
}

/* Large Desktop */
@media screen and (min-width: 1200px) {
    .zen-header__info {display: none;}
}

@media screen and (max-width: 640px) {
    div#facets { max-height: 100%!important; }
}

/* Mobile Facets Panel Styles */
@media screen and (max-width: 991px) {
  .zen-facet__main-filters {
    position: fixed;
    top: 32px;
    left: 0;
    width: 100%;
    height: 100vh;
    background: white;
    z-index: 9999 !important;
    padding: 15px;
    transform: translateX(-100%);
    transition: transform 0.3s ease-in-out;
    max-width: none;
  }

  .top-offer-bar {
        margin-top: 32px;
  }

  .zen-facet__main-filters span.label {
    font-size: 18px;
    line-height: 22px;
    font-weight: 700;
    display: block;
    margin-bottom: 15px;
  }

  .zen-filters-open .zen-facet__main-filters {
    transform: translateX(0);
  }

  .zen-search__refine-header {
    padding: 0 15px;
  }

  .zen-facet__body {
    padding: 15px 15px 0 15px;
  }

  .zen-search__refine-footer {
    position: fixed;
    bottom: 32px;
    left: 0;
    width: 100%;
    background: black;
    padding: 15px;
    z-index: 9999;
  }

  .zen-facet__main-filters .zen-form {
    padding-left: 0;
  }

  .zen-facet__main-filters .zen-form input {
    padding-left: 15px;
    color: black;
  }

  .zen-facet__main-filters .zen-form input::placeholder {
    color: black;
  }

  .zen-facet__main-filters .zen-cta.js-overlay-trigger {
    margin-left: -15px;
  }

  .zen-facet__main-filters .refine-header-title {
    padding-left: 0 !important;
  }

  .zen-facet__main-filters .clear-filters {
    color: #aaa !important;
    font-size: 13px !important;
    text-decoration: underline !important;
  }

  #mobileStartDate, #mobileEndDate {
    border: 1px solid black;
    border-radius: 9999px;
    padding: 8px 8px 8px 15px;
    background: transparent;
    width: 100%;
    color: black;
    font-size: 14px;
    line-height: 150%;
    font-weight: 500;
  }

  #mobileStartDate:focus, #mobileEndDate:focus {
    background: transparent;
    outline: none;
  }

  .zen-facet__main-filters .search-input {
    color: black;
    font-size: 14px;
    line-height: 150%;
    font-weight: 500;
  }


}

.zen-facet__main-filters .zen-facet__clear {
    top: 15px;
  }

  #keyword-range-facet .zen-facet__clear {
    top: 0;
  }


#mobile-menu .custom {
    display: none;
}

.zen-pagination__link:hover {
    background-color: #fe7720!important;
    border-radius: 9999px!important;
  }
.zen-pagination__link:hover:not(.pagination__first > .zen-pagination__link):not(.pagination__last > .zen-pagination__link) {
  background-color: #d3d6db!important;
  color: #000

}

.zen-form .search-input,
.zen-form .calendar-control {
  padding-left: 15px;
}

.zen-form .search-input::placeholder,
.zen-form .calendar-control::placeholder {
  color: #000;
}

.zen-facet__body .label {
  display: block;
  font-size: 18px;
  line-height: 22px;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.zen-results-count {
  font-size: 18px;
  line-height: 22px;
  font-weight: 700;
}

.daterangepicker td.active,
.daterangepicker td.active:hover,
.daterangepicker td.in-range,
.daterangepicker td.in-range:hover,
.daterangepicker td.available:hover,
.daterangepicker th.available:hover,
.daterangepicker .ranges li {
  background-color: #fe7720 !important;
}

.daterangepicker .applyBtn {
  background-color: #FE7720 !important;
  border-radius: 500px !important;
  color: #000 !important;
  border: 1px solid #FE7720 !important;
  min-height: auto !important;
  display: inline-flex !important;
  align-items: center !important;
  gap: 8px !important;
  padding: 6px 12px !important;
}

.daterangepicker .applyBtn:hover {
  background-color: #FFB347 !important;
  border: 1px solid #FFB347 !important;
}

.daterangepicker .cancelBtn {
  font-size: 14px;
  font-weight: 400;
  color: #7384a0;
  border: none;
  padding: 0;
  text-decoration: underline;
}

.daterangepicker .ranges .zen-filter-next,
.daterangepicker .ranges .active {
  border: 1px solid #000;
  background-color: transparent!important;
  border-radius: 9999px;
  color: #000!important;
}

.daterangepicker {
  font-family: fustatregular;
}

.daterangepicker td,
.daterangepicker th,
.daterangepicker .calendar-table,
.daterangepicker .ranges li,
.daterangepicker .calendar-table th.month,
.daterangepicker .calendar-table td.available,
.daterangepicker .calendar-table td.off,
.daterangepicker .calendar-table td.off.in-range,
.daterangepicker .calendar-table td.off.start-date,
.daterangepicker .calendar-table td.off.end-date,
.daterangepicker .calendar-table td.off.active,
.daterangepicker .calendar-table td.off:hover,
.daterangepicker .calendar-table td.available:hover,
.daterangepicker .calendar-table td.in-range,
.daterangepicker .calendar-table td.start-date,
.daterangepicker .calendar-table td.end-date,
.daterangepicker .calendar-table td.active,
.daterangepicker .calendar-table td.active:hover,
.daterangepicker .calendar-table td.available:hover,
.daterangepicker th.available:hover,
.daterangepicker .ranges li,
.daterangepicker .ranges li:hover,
.daterangepicker .ranges li.active,
.daterangepicker .ranges li.active:hover {
  font-family: fustatregular;
}

.daterangepicker .ranges li.active {
    border: 1px solid #000!important;
    display: none!important;
}

.range_inputs .daterangepicker_start_input,
.range_inputs .daterangepicker_end_input {
    display: none!important;

}

.daterangepicker td:not(.in-range) {
    border-radius: 9999px!important;
}

.daterangepicker td.start-date {
    border-top-right-radius: 0!important;
    border-bottom-right-radius: 0!important;
}

.daterangepicker td.end-date {
    border-top-left-radius: 0!important;
    border-bottom-left-radius: 0!important;
}

.daterangepicker td.in-range {
background-color: #fbe5d4!important;
}
.daterangepicker td.start-date.end-date {
    border-radius: 9999px!important;
}

.range_inputs {
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.range_inputs .applyBtn {
  font-family: fustatregular;
  font-weight: 500;
  text-align: center;
  border: 1px solid #000;
  background: transparent;
  justify-content: center;
  display: flex;
}

.range_inputs .cancelBtn {
  font-family: fustatregular;
  font-weight: 500;
  text-align: center;
  border: none;
  background: transparent;
}

.stretched-link {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1;
  pointer-events: auto;
  content: "";
  background-color: rgba(0,0,0,0);
}

.zen-media__content {
  color: black;
}

.zen-holiday__guide .zen-media {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  background-color: #fe7720;
  border-radius: 10px;
  padding: 20px;
}

.zen-holiday__guide .zen-media__image img {
  max-height: 100px;
  width: auto;
  object-fit: contain;
}

.zen-media .flex-shrink-0.download-btn {
  margin-left: 20px;
}

.zen-media .btn.btn-primary {
  background-color: white;
  border-color: white;
  padding: 8px 16px;
  gap: 8px;
  min-height: 38px;
  margin: 5px;
}

.zen-media .btn.btn-primary:hover {
  background-color: #f5f5f5;
  border-color: #f5f5f5;
}

.zen-media__content p:last-child {
  margin-bottom: 0;
}

.overview-section--dark {
  background-color: black;
  padding: 60px 0;
}

.overview-section--dark h3,
.overview-section--dark p {
  color: white;
}

.overview-section--dark p strong {
  margin-bottom: 2px;
}

.zen-holiday__content-box { border: none; }

.dates-prices-sidebar .zen-card {
    padding: 30px;
    border: none;
    box-shadow: 4px 4px 4px 0px rgba(0, 0, 0, 0.25);
}

.dates-prices-sidebar .zen-card h3 {
    padding: 0 30px 20px 30px;
    border-bottom: 1px solid #E0D8D2;
    margin: 0 -30px 20px -30px ;
}


.dates-prices-sidebar h2.zen-title,
.dates-prices-sidebar h2.zen-title--sub,
.dates-prices-sidebar h2.zen-title--sub-2 {
    font-size: 24px;
    line-height: 28px;
    font-weight: 700;
    margin-bottom: 1.5rem;
}

.dates-prices-sidebar h3.zen-title,
.dates-prices-sidebar h3.zen-title--sub,
.dates-prices-sidebar h3.zen-title--sub-2 {
    padding: 30px;
    font-size: 18px;
    line-height: 22px;
    font-weight: 700;
    margin-bottom: 1.5rem;
}

/* Mobile accordion tab styling */
.js-mobile-tabs {
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    padding: 21px 20px 21px 16px;
    border: none;
    background: none;
    width: 100%;
    text-align: center;
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid #909090;
    border-radius: 0;
}

.js-mobile-tabs span {
    flex: 1;
    text-align: center;
    color: #000!important;
}

.js-mobile-tabs:after {
    font-family: fontello;
    content: "\e81d";
    font-size: inherit;
    margin-left: auto;
    transition: transform 0.3s ease;
    color: #000!important;
}

.js-mobile-tabs:last-child {
    border-bottom: 1px solid #909090;
}

.js-mobile-tabs[aria-expanded="true"] {
    border-bottom: 8px solid #ff6b00;
    padding-bottom: 13px;
    border-top: 1px solid #909090;
    background: transparent;
    color: inherit;
}

.js-mobile-tabs[aria-expanded="true"]:after {
    transform: rotate(180deg);
}

.js-mobile-tabs[aria-expanded="true"]:last-child {
    border-bottom: 8px solid #ff6b00;
}

.js-mobile-tabs:before {
    content: "";
    display: block;
    width: 40px;
    height: 40px;
    margin-right: 10px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

.js-mobile-tabs:hover {
    background-color: transparent;
}

.js-mobile-tabs .conditional-break {
    display: inline-block; /* Prevents break on mobile by default */
    width: 3px;            /* No space on mobile by default */
}

/* Mobile tab icons */
[data-bs-target="#overview-tab-content"].js-mobile-tabs:before,
[data-bs-target="#trip-overview-tab-content"].js-mobile-tabs:before {
    background-image: url('/templates/zenbase/icons/trips/overview.svg');
}

[data-bs-target="#itinerary-tab-content"].js-mobile-tabs:before {
    background-image: url('/templates/zenbase/icons/trips/itinerary.svg');
}

[data-bs-target="#dates-prices-tab-content"].js-mobile-tabs:before {
    background-image: url('/templates/zenbase/icons/trips/dates_prices.svg');
}

[data-bs-target="#food-accommodation-tab-content"].js-mobile-tabs:before {
    background-image: url('/templates/zenbase/icons/trips/food_accomm.svg');
}

[data-bs-target="#trip-extensions-tab-content"].js-mobile-tabs:before,
[data-bs-target="#extras-tab-content"].js-mobile-tabs:before {
    background-image: url('/templates/zenbase/icons/trips/extensions.svg');
}

[data-bs-target="#faqs-tab-content"].js-mobile-tabs:before {
    background-image: url('/templates/zenbase/icons/trips/faqs.svg');
}

@media (max-width: 991.98px) {
    .zen-holiday__content-box {
        margin: 0;
        padding: 30px 0;
        border: none;
    }
    .zen-holiday__guide .zen-media__image img {
        max-height: 100%;
      }

}

.container {
    max-width: 1440px;
    margin: 0 auto;
}

.text-success {
  color: #95C623;
}

.text-orange {
  color: #CD5334;
}

.zen-text--price {
  font-size: 16px;
  font-weight: 700;
  color: #fe7720;
}

.zen-text--price-instalment {
  font-size: 14px;
  font-weight: 400;
  color: black;
}

.zen-text--price-instalment-value {
  font-size: 14px;
  font-weight: 600;
  color: black;
}

.zen-footer, .zen-footer .mod-sppagebuilder {
    max-width: 100%;
}

.extension-card__content > div:first-of-type h3 {
    margin: 0;
}

.zen-header__menu .zen-menu__list li[data-item-id="727"] a {
    padding-right: 0;}

.zen-header__menu .zen-menu__list li[data-item-id="727"] .zen-menu__icon {
    margin: 0;
}

/* header fixes */

@media screen and (min-width: 768px) and (max-width: 991px) {
    /* hero bottom padding */
    body .sp-page-builder .page-content #section-id-1733150149652 {
        margin-top: 0!important;
    }
    /* page top padding */

}

@media screen and (min-width: 577px) and (max-width: 992px) {
    /* hero bottom padding */
    body .sp-page-builder .page-content #section-id-1733150149652 {
        margin-top: 0!important;
    }
    /* page top padding */
    .top-offer-bar {
        margin-top: 32px;
    }

}

@media screen and (max-width: 1199px) {
    body:not(.homepage) .zen-header
 {
        position: static;
        margin-top: 38px;
    }
}

  @media screen and (min-width: 1200px) {
    body:not(.homepage) .zen-header {
      margin-top: 38px;
    }
  }
/* @media screen and (max-width: 992px) {
body:not(.homepage) .zen-header {
    margin-top: 51px!important;
}

.homepage .zen-header {
    top: 0 !important;
    /* margin-top: 0!important; */
}
} */

/* @media screen and (min-width: 841px) {

    body .homepage .zen-header {
        top: 146px !important;
    }

}

@media screen and (max-width: 840px) {
    body .homepage .zen-header {
        top: 134px !important;
    }
} */

/* @media screen and (min-width: 992px) {

    html body .homepage header.zen-header {
        top: 178px !important;
    }

} */


      .zen-header-logo { padding: 10px 0;}


      h3 b, h4 b { font-weight: 800;}


body:not(.homepage).zen-header {margin-top: 32px;}
/* .homepage .zen-header { top: 70px; } */

@media screen and (max-width: 1199px) {

    .homepage .zen-header {  top: 0; }
}

.zen-menu__link {transition: color 0.2s ease;}

a:hover, a:focus, 
.zen-menu__link:hover, .zen-menu__link:focus,
.zen-menu__item:has(.zen-menu__panel:hover) .zen-menu__link,
.zen-menu__item:has(.zen-menu__panel:focus) .zen-menu__link {
    color: #777;
}

/* BLOG SEARCH FIXES */

.zen-content-search .highlight { margin: 0}

.zen-content-search .zen-card { border-radius: 10px; }

.zen-content-search__main-form { position: relative;}

.zen-content-search__main-form .zen-input { border-color: #777!important; }

.zen-content-search__main-form button { position: absolute; right: 0; top: 9px;}

.zen-content-search__buttons button {color: #202020}

.zen-content-search__main-form button:hover, .zen-content-search__main-form button:active { color: #666; }

.zen-content-search__main-form .icon-sys-search { font-size: 22px; }

.zen-content-search__main-panel { padding: 10px 0 20px 0!important; }

.zen-content-search__filters { border-radius: 10px;  }

.zen-content-search__result-select { border: 1px #999 solid; border-radius: 9999px; padding: 0 15px 0 20px; }

.zen-content-search__result-select select { border: none; padding: 10px; background: transparent}

.zen-content-search__result-select span { padding: 0}

.zen-content-search__filters { margin: 0; border: 1px solid #ddd; }

.ordering-box button { flex-grow: 0}

.ordering-box > div { gap: 10px }

.ordering-selectbox { position: relative; top: 4px; }

.ordering-selectbox select { text-transform: none; }

.ordering-box button:hover { background-color: #FFB347!important; }

.zen-content-search__filter-ordering .hidden { text-transform: none; border: 1px solid #777; border-radius: 999px; padding: 13px 20px;}

.zen-content-search__filter-ordering .hidden:after { background-color: transparent; border: none; padding: 17px 20px; font-size: 10px;}

.zen-content-search__filter-string .radio,
.zen-content-search__filter-category .checkbox {margin: 0px 10px 7px 0px;}

.zen-content-search .selectlist select { height: auto; padding-top: 10px; padding-bottom: 10px;}

.zen-content-search__result-select select { outline: none; text-align: right; -webkit-appearance: none}

.zen-content-search .selectlist span {color: rgb(35, 56, 60) }

.zen-content-search .selectlist span:after { content: "\e859"; color: #555 }

#searchForm .h4 { margin-bottom: .75rem }

.zen-content-search__result-select { position: relative; width: auto; padding: 10px 65px 10px 20px}

.zen-content-search__result-select select { 
    /* background-color: #ff000099; */
    position: absolute;
    width: 78%;
    right: 30px;
    text-align: right;
    text-indent: 75px;
    color: #202020
}

    .zen-content-search__result-select:after {
content: "\e859";
color: #555;
font-family: "fontello";
position: absolute;
right: 18px;
font-size: 9px;
}
    
    @media screen and (max-width: 450px) {

    .zen-content-search__result-select span {  }

    .ordering-box .ordering-selectbox { width: 100%; margin: 0; position: static;}
    .ordering-box button { width: 100%; max-width: 500px; margin: 0!important}
    .zen-content-search__results-topper { justify-content: space-between;}
}

.zen-header {
    margin-top: 38px;
    /* position: static; */
}

.icon-sys-hamburger { color: #333; }

/* Fix card info alignment - allow Bootstrap grid to work properly */
.zen-card__info {
    /* Ensure proper spacing from card edges */
    padding-left: 0 !important;
    padding-right: 0 !important;
}

/* Ensure Bootstrap row spans full width */
.zen-card__info .row {
    margin-left: 0;
    margin-right: 0;
    width: 100%;
}

/* Ensure child flex containers work properly */
.zen-card__info > .d-flex {
    gap: 8px; /* Add small gap between icon and text */
}

/* ==========================================================================
   Info Icon & Popover Styles
   ========================================================================== */

/* Info icon styling */
.info-icon {
    color: white;
    cursor: pointer;
    margin-left: 4px;
    display: inline-block;
    vertical-align: middle;
    transition: opacity 0.2s ease;
    width: 19.2px; /* 20% larger than 16px */
    height: 19.2px;
}

.info-icon:hover {
    opacity: 0.8;
}

.info-icon svg {
    width: 100%;
    height: 100%;
    display: block;
}

/* Ensure proper spacing in different contexts */
.info-icon:not(:first-child) {
    margin-left: 4px;
}

.info-icon:not(:last-child) {
    margin-right: 2px;
}

/* Popover styling */
.popover {
    border-radius: 12px;
    border: none !important;
    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.2) !important;
}

.popover .popover-body {
    padding: 16px !important;
    max-width: 300px;
    font-size: 14px !important;
    line-height: 1.4 !important;
    font-weight: 400 !important;
    color: #666 !important;
}

/* Info icon hover area - expand clickable area without shrinking icon */
.info-icon {
    display: inline-block;
    cursor: pointer;
    position: relative;
}

.info-icon::before {
    content: '';
    position: absolute;
    top: -6px;
    left: -6px;
    right: -6px;
    bottom: -6px;
    z-index: -1;
}

.popover .popover-header {
    background-color: white;
    border-bottom: 2px solid #f5f5f5;
    font-size: 24px !important;
    line-height: 26px !important;
    font-weight: 800 !important;
    color: #4f4f4f !important;
    padding: 12px 16px !important;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
}

/* Ensure proper spacing and styling for HTML content in popovers */
.popover-content,
.popover-content * {
    font-size: 14px !important;
    line-height: normal !important;
    color: #4f4f4f !important;
}

.popover-content p {
    margin-bottom: 8px;
    font-size: 14px !important;
    line-height: normal !important;
    color: #4f4f4f !important;
}

.popover-content p:last-child {
    margin-bottom: 0;
}

.popover-content strong,
.popover-content b {
    font-weight: 700 !important;
    color: #4f4f4f !important;
}

.popover-content a {
    /* Allow links to use their standard orange color */
    text-decoration: underline;
}

/* Enforce body text styling for common HTML elements in popovers */
.popover p, .popover ul, .popover ol, .popover th, .popover td {
    font-size: 14px !important;
    line-height: normal !important;
    font-weight: 400 !important;
    color: #666 !important;
}

/* Info icon positioning - shift up by 2px and reduce left margin */
.info-icon {
    transform: translateY(-2px);
    display: inline-block;
    margin-left: 2px !important; /* Half the default margin */
}

.popover-content em,
.popover-content i {
    font-style: italic;
}

.popover-content br {
    line-height: 1.6;
}

/* Mobile-specific popover adjustments */
@media (max-width: 768px) {
    .popover {
        max-width: calc(100vw - 40px) !important; /* Ensure popover doesn't exceed viewport width */
        margin: 0 20px; /* Add margin from viewport edges */
    }

    .popover .popover-body {
        max-width: none; /* Remove max-width restriction on mobile */
        padding: 12px !important; /* Slightly reduce padding on mobile */
    }

    /* Ensure bottom-placed popovers have proper spacing */
    .popover.bs-popover-bottom {
        margin-top: 8px;
    }

    /* Ensure top-placed popovers have proper spacing */
    .popover.bs-popover-top {
        margin-bottom: 8px;
    }
}